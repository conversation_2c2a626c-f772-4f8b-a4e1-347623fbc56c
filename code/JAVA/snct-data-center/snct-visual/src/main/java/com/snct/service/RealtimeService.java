package com.snct.service;

import com.alibaba.fastjson.JSONObject;
import com.snct.common.RedisParameter;
import com.snct.common.core.domain.entity.SysDept;
import com.snct.common.utils.DateUtils;
import com.snct.hbase.domain.hbase.AttitudeHbaseVo2;
import com.snct.hbase.domain.hbase.AwsHbaseVo;
import com.snct.hbase.enums.DeviceTypeEnum;
import com.snct.hbase.utils.HBaseDaoUtil;
import com.snct.service.device.AttitudeService;
import com.snct.service.device.AwsService;
import com.snct.service.device.DeviceService;
import com.snct.service.device.GpsService;
import com.snct.system.domain.Device;
import com.snct.system.domain.Ship;
import com.snct.system.domain.dto.ShipSimpleDto;
import com.snct.system.service.IShipService;
import com.snct.system.service.ISysDeptService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * aws 操作类
 */
@Service
@Transactional(readOnly = true)
public class RealtimeService {
    private Logger logger = LoggerFactory.getLogger(RealtimeService.class);

    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private AwsService awsService;
    @Autowired
    private AttitudeService attitudeService;
    @Autowired
    private GpsService gpsService;
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private IShipService shipService;
    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    /**
     * 获取数据公用方法
     */
    public String getReidsNewData(String sn, String deviceCode) {
        String jsonString = null;
        ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
        if (redisTemplate.hasKey(RedisParameter.LATEST_DATA + sn + "_" + deviceCode)) {
            jsonString = valueOperations.get(RedisParameter.LATEST_DATA + sn + "_" + deviceCode);
        }
        return jsonString;
    }

    public Object getLatestData(String sn, String deviceCode) {

        // 根据船舶sn查询设备列表
        List<Device> deviceList = getDeviceListBySn(sn);

        String str = getReidsNewData(sn, deviceCode);

        sn = sn.trim().replace("SN", "");

        //if (StringUtils.isNotBlank(str)) {
        //    return JSONObject.parseObject(str);
        //}

        Device device = deviceService.getDeviceBySnAndCode(sn, deviceCode);
        if (device == null) {
            return null;
        }
        Long deptId = device.getDeptId();
        Long deviceId = device.getId();

        long currentTimeMillis = System.currentTimeMillis();
        Long startTimeMillis = com.snct.hbase.utils.DateUtils.addDay(currentTimeMillis, -90);
        String startTime= DateUtils.transferTimeStamp(startTimeMillis);
        String lastTime= DateUtils.transferTimeStamp(currentTimeMillis);

        String startRowKey = hBaseDaoUtil.buildStartRowKey(deptId, sn, deviceId, startTime, true);
        String endKey = hBaseDaoUtil.buildEndRowKey(deptId, sn, deviceId, lastTime, true);

        Object object = null;

        if (DeviceTypeEnum.AWS.getValue().equals(device.getType())) {
            //object = awsService.getLatestDataFromHbase(sn, deviceCode);
            object = awsService.getLatestDataFromHbase(deptId.toString(),sn);
        }

        if (DeviceTypeEnum.ATTITUDE.getValue().equals(device.getType())) {
            //object = awsService.getLatestDataFromHbase(sn, deviceCode);
            object = attitudeService.getLatestDataFromHbase(deptId.toString(), sn);
        }

        ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
        valueOperations.set(RedisParameter.LATEST_DATA + sn + "_" + deviceCode, JSONObject.toJSONString(object), 3, TimeUnit.MINUTES);

        return object;
    }



    /**
     * zhoufd
     * 数据包代码说明：
     * 0A01 入驻企业列表
     * 0A02 入驻企业/船只数量汇总
     * 0A03 天气信息/预警信息（暂定）
     * 0A04 地图数据-企业名称与坐标列表
     * 0B01 船基本信息
     * 0B02 姿态信息
     * 0B03 气象信息信息
     * 0B04 天气信息/预警信息（暂定）
     * 0B05 企业总接入船只数量
     * 0B06 地图数据-船只名称与坐标列表
     * 0C01 视频监控实时播放地址列表
     * 0C02 视频监控历史记录列表
     * 0D01 船基本信息  （与  0B01可以通用）
     * 0D02 历史轨迹节点信息
     * 0E01 PDU信息
     * 0E02 卫星猫信息
     * 0E03 功放信息
     * 0F01 根据船舶SN查询设备列表
     */
    public Object getSrceenLatestData(String deptId,String sn, String dataCode) {

        Object object = null;

        if("0A01".equals(dataCode)){
            return getDeptList();             //入驻企业列表
        }
        if("0A02".equals(dataCode)){
            return getDeptStatistics();       //入驻企业与船只汇总
        }
        if("0A03".equals(dataCode)){
            return getWarningInfo();          //预警/天气
        }
        if("0A04".equals(dataCode)){
            return getShipListDetail(deptId);   //船只列表 带经纬度
        }
        //if("0A04".equals(dataCode)){
        //    return getDeptDetailList();       //入驻企业列表 带经纬度
        //}
        if("0B01".equals(dataCode)){
            return getShipDetail(sn);         //船只信息
        }
        if("0B02".equals(dataCode)){
            return getAttitudeDetail(sn);      //姿态仪最新数据信息
        }
        if("0B03".equals(dataCode)){
            return getAwsDetail(sn);          //气象仪最新数据信息
        }
        if("0B04".equals(dataCode)){
            return object = new HashMap<>();   //预警/天气
        }
        if("0B05".equals(dataCode)){
            return getShipStatistics(deptId);  //企业总接入船只数量
        }
        if("0B06".equals(dataCode)){
            return getShipListDetail(deptId);   //地图数据-船只名称与坐标列表
        }
        if("0C01".equals(dataCode)){
            return object = new HashMap<>();   //视频监控实时播放地址列表
        }
        if("0C02".equals(dataCode)){
            return object = new HashMap<>();   //视频监控历史记录列表
        }
        if("0D01".equals(dataCode)){
            return getShipDetail(sn);          //船基本信息  （与  0B01可以通用）
        }
        if("0D02".equals(dataCode)){
            return getShipGpsList(sn);         //历史轨迹节点信息
        }
        if("0E01".equals(dataCode)){
            return getPduInfoList(sn);         //PDU信息
        }
        if("0E02".equals(dataCode)){
            return getModemInfoList(sn);       //卫星猫信息
        }
        if("0E03".equals(dataCode)){
            return getAmplifierList(sn);       //功放信息
        }
        if("0F01".equals(dataCode)){
            return getSimpleDeviceListBySn(sn); //根据船舶SN查询设备列表
        }
        return object;
    }

    //读取企业列表
    private Object getDeptList(){
        List<SysDept> dlist = deptService.selectDeptByPid(0l);
        List<Map> relist = new ArrayList<>();;
        if(dlist.size()==1){
            SysDept sysDept =  dlist.get(0);
            List<SysDept>  list = deptService.selectDeptByPid(sysDept.getDeptId());
            for(SysDept dept : list){
                Map map = new HashMap();
                map.put("deptId", dept.getDeptId());
                map.put("deptName", dept.getDeptName());
                map.put("status", dept.getStatus());
                relist.add(map);
            }
        }
        return relist;
    }
    //读取企业列表带经纬度
    private Object getDeptDetailList(){
        List<SysDept> dlist = deptService.selectDeptByPid(0l);
        List<Map> relist = new ArrayList<>();
        if(dlist.size()==1){
            SysDept sysDept =  dlist.get(0);
            List<SysDept>  list = deptService.selectDeptDetailByPid(sysDept.getDeptId());
            for(SysDept dept : list){
                Map map = new HashMap();
                map.put("deptId", dept.getDeptId());
                map.put("deptName", dept.getDeptName());
                map.put("status", dept.getStatus());
                map.put("latitude", dept.getLatitude());
                map.put("longitude", dept.getLongitude());
                relist.add(map);
            }
        }
        return relist;
    }

    //入驻企业汇总，船只汇总 统计
    private Object getDeptStatistics(){
        List<SysDept> dlist = deptService.selectDeptByPid(0l);
        Map<String, Integer> remap = new HashMap<>();
        if(dlist.size()==1){
            SysDept sysDept =  dlist.get(0);
            remap.put("enterprise_num",deptService.selectDeptByPid(sysDept.getDeptId()).size());
            remap.put("ship_num",shipService.selectShipListCount(new Ship()));
        }else{
            remap.put("enterprise_num",0);
            remap.put("ship_num",0);
        }
        return remap;
    }

    //预警信息
    private Object getWarningInfo(){
        //暂无天气信息与预警信息
        return new HashMap<String, Object>();
    }

    //船的基本信息
    private Object getShipDetail(String sn){

        Ship ship = shipService.selectShipByShipSn(sn);
        if(ship!=null){
            Map<String, Object> map = new HashMap<>();
            map.put("name",ship.getName());
            map.put("mmsi", ship.getMmsi());
            map.put("callSign", ship.getCallSign());
            map.put("sn", sn);
            getLatestData(sn, "032A");


//            map.put("longitude",gps.get);
//            map.put("latitude",gps.get);
//            map.put("utc",gps.get);
//            map.put();
            return map;
        }
        return new HashMap<String, Object>();
    }

    private Object getAttitudeDetail(String sn){
        //获取姿态信息
        AttitudeHbaseVo2 latestData = (AttitudeHbaseVo2) getLatestData(sn, "033B");
        Map<String, Object> map = new HashMap<>();
        map.put("attitudeHeading",latestData.getHeading());   //船首向
        map.put("attitudeRolling", latestData.getRolling());
        map.put("attitudePitch", latestData.getPitch());
        map.put("attitudeLongitude",latestData.getLon());
        map.put("attitudeLatitude",latestData.getLat());
        map.put("attitudeDistance", "");
        map.put("attitudeSpeed", "");     //船速度
        map.put("attitudeUptime", latestData.getInitialBjTime());    //数据更新时间
        map.put("attitudeSn", sn);
        return map;
    }

    private Object getAwsDetail(String sn){
        //获取气象信息
        AwsHbaseVo latestData = (AwsHbaseVo) getLatestData(sn, "038A");
        Map<String, Object> map = new HashMap<>();
        map.put("awsSpeed", latestData.getRelativeWindSpeed());
        map.put("awsDirection", latestData.getRelativeWind());
        map.put("awsUptime", latestData.getInitialBjTime());
        map.put("awsSn", sn);
        return map;
    }

    private Object getShipStatistics(String deptId){
        List<ShipSimpleDto> list = shipService.selectSimpleShipListByDeptId(Long.parseLong(deptId));
        Map<String, Integer> remap = new HashMap<>();
        remap.put("shipNum",list.size());
        return remap;
    }

    private Object getShipListDetail(String deptId){
        List<ShipSimpleDto> list = shipService.selectSimpleShipListByDeptId(Long.parseLong(deptId));
        List<Map> relist = new ArrayList<>();
        for(ShipSimpleDto shipSimpleDto : list){
            Map<String, String> remap = new HashMap<>();
            remap.put("name",shipSimpleDto.getName());
            remap.put("sn",shipSimpleDto.getSn());
            remap.put("shipId",shipSimpleDto.getShipId()+"");
            remap.put("longitude","");
            remap.put("latitude","");
            relist.add(remap);
        }
        return relist;
    }

    private Object getShipGpsList(String sn){
        return new HashMap<String, Object>();
    }
    private Object getPduInfoList(String sn){
        return new HashMap<String, Object>();
    }
    private Object getModemInfoList(String sn){
        return new HashMap<String, Object>();
    }
    private Object getAmplifierList(String sn){
        return new HashMap<String, Object>();
    }

    /**
     * 根据船舶sn查询设备列表
     * @param sn 船舶sn
     * @return 设备列表
     */
    public List<Device> getDeviceListBySn(String sn) {
        if (sn == null || sn.trim().isEmpty()) {
            logger.warn("船舶SN为空，无法查询设备列表");
            return new ArrayList<>();
        }

        try {
            // 清理sn，去除可能的"SN"前缀
            String cleanSn = sn.trim().replace("SN", "");

            // 先尝试从Redis缓存获取
            ValueOperations<String, List<Device>> valueOperations = redisTemplate.opsForValue();
            String cacheKey = "DEVICE_LIST_" + cleanSn;
            List<Device> cachedDeviceList = valueOperations.get(cacheKey);

            if (cachedDeviceList != null && !cachedDeviceList.isEmpty()) {
                logger.debug("从缓存获取到船舶{}的设备列表，共{}个设备", cleanSn, cachedDeviceList.size());
                return cachedDeviceList;
            }

            // 缓存中没有，从数据库查询
            List<Device> deviceList = deviceService.queryListBySn(cleanSn);

            if (deviceList != null && !deviceList.isEmpty()) {
                // 将结果缓存5分钟
                valueOperations.set(cacheKey, deviceList, 5, TimeUnit.MINUTES);
                logger.info("查询到船舶{}的设备列表，共{}个设备", cleanSn, deviceList.size());
            } else {
                logger.warn("未查询到船舶{}的设备列表", cleanSn);
                deviceList = new ArrayList<>();
            }

            return deviceList;

        } catch (Exception e) {
            logger.error("查询船舶{}设备列表时发生异常: {}", sn, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据船舶sn查询设备列表的简化信息
     * @param sn 船舶sn
     * @return 设备简化信息列表
     */
    public List<Map<String, Object>> getSimpleDeviceListBySn(String sn) {
        List<Device> deviceList = getDeviceListBySn(sn);
        List<Map<String, Object>> result = new ArrayList<>();

        for (Device device : deviceList) {
            Map<String, Object> deviceInfo = new HashMap<>();
            deviceInfo.put("id", device.getId());
            deviceInfo.put("name", device.getName());
            deviceInfo.put("code", device.getCode());
            deviceInfo.put("type", device.getType());
            deviceInfo.put("enable", device.getEnable());
            deviceInfo.put("deptId", device.getDeptId());
            deviceInfo.put("connectStatus", device.getConnectStatus());
            result.add(deviceInfo);
        }

        return result;
    }


}
