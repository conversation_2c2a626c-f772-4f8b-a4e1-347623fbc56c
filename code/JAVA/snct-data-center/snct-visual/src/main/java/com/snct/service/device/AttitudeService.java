package com.snct.service.device;


import com.snct.hbase.domain.hbase.AttitudeHbaseVo2;
import com.snct.hbase.enums.DeviceTypeEnum;
import com.snct.hbase.utils.HBaseDaoUtil;
import com.snct.system.domain.Device;
import com.snct.system.service.impl.DeviceServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: AttitudeService
 * @Description: 姿态数据服务类
 * @author: wzewei
 * @date: 2025-07-30 14:53
 */
@Service
@Transactional(readOnly = true)
public class AttitudeService {

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    @Autowired
    private DeviceServiceImpl deviceService;

    private Logger logger = LoggerFactory.getLogger(AttitudeService.class);
    /**
     * 根据时间范围来查询数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public List<AttitudeHbaseVo2> queryByTime(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {

        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.ATTITUDE.getAlias(), deviceCode, interval);
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);

        return hBaseDaoUtil.scanByRowList(new AttitudeHbaseVo2(), tableName, rowList);
    }

    public AttitudeHbaseVo2 getLatestDataFromHbase(String deptId, String sn) {

        // 根据部门ID、sn 获取设备列表
        List<Device> list = deviceService.selectSimpleDeviceListBySnAndType(sn, Long.valueOf(DeviceTypeEnum.ATTITUDE.getValue()));

        String prefixKey = hBaseDaoUtil.buildPrefixKey(Long.valueOf(deptId), sn, list.get(0).getId(), true);

        String tableName = "snct:attitude";
        return hBaseDaoUtil.getLatestRecord(new AttitudeHbaseVo2(), tableName, prefixKey);
    }

}
